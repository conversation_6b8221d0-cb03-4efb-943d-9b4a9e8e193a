import type {Preview} from "@storybook/react";
import {GluestackUIProvider} from "../components/ui/gluestack-ui-provider";
import {LanguageProvider} from "../contexts/LanguageContext";
import "../global.css";
import "../i18n.config";

const preview: Preview = {
    parameters: {
        controls: {
            matchers: {
                color: /(background|color)$/i,
                date: /Date$/,
            },
        },
    },
    decorators: [
        (Story, context) => {
            const mode = context.globals.theme || "light";
            return (
                <GluestackUIProvider mode={mode}>
                    <LanguageProvider>
                        <Story />
                    </LanguageProvider>
                </GluestackUIProvider>
            );
        },
    ],
    globalTypes: {
        theme: {
            description: "Global theme for components",
            defaultValue: "light",
            toolbar: {
                title: "Theme",
                icon: "circlehollow",
                items: [
                    {value: "light", icon: "circlehollow", title: "Light"},
                    {value: "dark", icon: "circle", title: "Dark"},
                    {value: "system", icon: "browser", title: "System"},
                ],
                showName: true,
                dynamicTitle: true,
            },
        },
    },
};

export default preview;
